from fastapi import FastAPI, Request, HTTPException, Depends, UploadFile, File, Form, status
from fastapi.responses import JSONResponse, StreamingResponse, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import os
import re
import shutil
import time
import random
import io
import uuid
import sys
import logging
from datetime import timedelta, datetime
from PIL import Image, ImageDraw, ImageFont
import functools
import ctypes
from ctypes import wintypes
import uvicorn

# 导入数据库相关
from sqlalchemy import create_engine, Column, Integer, String, Text, Boolean, Float, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

# JWT相关
import jwt
from passlib.context import CryptContext

# 配置日志系统
def setup_logging():
    """配置应用日志系统"""
    # 创建logs目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 配置日志格式
    log_format = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y%m%d %H:%M:%S'
    )

    # 创建应用日志记录器
    app_logger = logging.getLogger('app')
    app_logger.setLevel(logging.INFO)
    # 防止日志传播到根记录器，避免重复
    app_logger.propagate = False

    # 统一日志文件（按日轮转）
    from logging.handlers import TimedRotatingFileHandler
    file_handler = TimedRotatingFileHandler(
        os.path.join(log_dir, 'app.log'),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setFormatter(log_format)
    file_handler.setLevel(logging.INFO)

    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_format)
    console_handler.setLevel(logging.INFO)

    # 添加处理器
    app_logger.addHandler(file_handler)
    app_logger.addHandler(console_handler)

    return app_logger


# 初始化日志系统
logger = setup_logging()



# 获取客户端IP地址的辅助函数
def get_client_ip(request: Request):
    """获取客户端真实IP地址"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.client.host

# 脱敏处理函数
def mask_sensitive_data(data, field_name):
    """对敏感数据进行脱敏处理"""
    if not data:
        return data

    if field_name in ['password', 'captcha']:
        return '***'
    elif field_name == 'phone':
        if len(str(data)) >= 7:
            return str(data)[:3] + '****' + str(data)[-4:]
        return '***'
    elif field_name == 'username':
        if len(str(data)) >= 3:
            return str(data)[:1] + '***' + str(data)[-1:]
        return '***'
    return data

# 创建FastAPI应用
logger.info("开始初始化应用")
app = FastAPI(
    title="华宇项目管理系统",
    description="基于FastAPI的项目管理系统",
    version="2.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["X-Captcha-ID"]
)

# 配置Gzip压缩
app.add_middleware(
    GZipMiddleware,
    minimum_size=500,
    compresslevel=6
)

logger.info("应用初始化完成")

# 数据库配置
logger.info("开始配置数据库")
DATABASE_URL = "mysql+pymysql://root:Aa1472356890@localhost:3306/yekuo"

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True,
    connect_args={
        "charset": "utf8mb4",
        "connect_timeout": 10,
        "read_timeout": 30,
        "write_timeout": 30,
        "autocommit": True,
    },
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 依赖注入：获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# JWT配置
SECRET_KEY = "safsaf1454sa878"  # 生产环境应从环境变量获取
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_HOURS = 8

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer认证
security = HTTPBearer()

# 配置常量
Sql_City = [
    "城区", "环城", "汝南", "平舆", "新蔡", 
    "确山", "泌阳", "正阳", "遂平", "西平", "上蔡"
]

# 地市权限控制配置
CITY_ACCESS_KEYS = {
    "cq2024": "城区",      # 城区访问key
    "hc2024": "环城",      # 环城访问key
    "rn2024": "汝南",      # 汝南访问key
    "py2024": "平舆",      # 平舆访问key
    "xc2024": "新蔡",      # 新蔡访问key
    "qs2024": "确山",      # 确山访问key
    "by2024": "泌阳",      # 泌阳访问key
    "zy2024": "正阳",      # 正阳访问key
    "sp2024": "遂平",      # 遂平访问key
    "xp2024": "西平",      # 西平访问key
    "sc2024": "上蔡",      # 上蔡访问key
    "admin2024": "ALL"     # 管理员key，可查看所有地市数据
}

# 所有项目类型
Pro_Lx = [
    {'name': "高压用户", 'num': 17},
    {'name': "低压用户", 'num': 10},
    {'name': "光伏低压自然人", 'num': 18},
    {'name': "光伏低压非自然人", 'num': 20},
    {'name': "光伏高压", 'num': 20},
    {'name': "华宇高压用户", 'num': 17},
]

# 创建项目类型到数量的映射字典，提高查找效率
PRO_TYPE_NUM_MAP = {item['name']: item['num'] for item in Pro_Lx}

# 文件上传配置
ALLOWED_EXTENSIONS = {"pdf"}
MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 限制上传大小为100MB

def get_executable_dir():
    """
    获取可执行文件所在目录的路径
    兼容开发环境和 PyInstaller 打包后的环境
    """
    if getattr(sys, 'frozen', False):
        # PyInstaller 打包后的环境
        # sys.executable 指向 .exe 文件的完整路径
        executable_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        # __file__ 指向当前 Python 脚本文件
        executable_dir = os.path.dirname(os.path.abspath(__file__))

    return executable_dir

# 获取可执行文件目录并设置相对路径
BASE_DIR = get_executable_dir()
sys_temp = BASE_DIR
logger.info(f'运行目录{BASE_DIR}')

# 使用内存字典存储验证码
captcha_store = {}

# 数据库模型定义
class User(Base):
    __tablename__ = "user"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(Text, unique=True, nullable=False)
    phone = Column(Text, unique=True, nullable=False)
    city = Column(Text, nullable=False)
    password = Column(Text, nullable=False)
    date = Column(Text, nullable=False, default=str(int(time.time())))

    def to_dict(self):
        return {
            "id": self.id,
            "username": self.username,
            "phone": self.phone,
            "city": self.city,
            "password": self.password,
            "date": self.date,
        }


class Baolist(Base):
    __tablename__ = "baolist"

    id = Column(Integer, primary_key=True, index=True)
    bao_name = Column(Text, nullable=False)
    bao_state = Column(Boolean, nullable=False)
    bao_lx = Column(Text, nullable=False)
    bao_bm = Column(Text, nullable=False)
    bao_city = Column(Text, nullable=False)
    bao_start = Column(Text, nullable=False, default=str(int(time.time())))
    bao_end = Column(Text, nullable=False)
    is_reject = Column(Boolean, nullable=False)
    is_approve = Column(Boolean, nullable=False)
    is_submit = Column(Boolean, nullable=False)
    usercode = Column(Text, nullable=False)
    gds = Column(Text, nullable=False)
    czry = Column(Text, nullable=False)

    # 新增字段
    gdlx = Column(Text, nullable=True, comment='工单类型：增容、临时用电、新装')  # 工单类型
    sj_ywsl = Column(Text, nullable=True, comment='业务受理时间')  # 业务受理时间
    sj_xckc = Column(Text, nullable=True, comment='现场勘察时间')  # 现场勘察时间
    sj_fadf = Column(Text, nullable=True, comment='方案答复时间')  # 方案答复时间
    sj_jgys = Column(Text, nullable=True, comment='竣工验收时间')  # 竣工验收时间
    sj_zbsd = Column(Text, nullable=True, comment='装表送电时间')  # 装表送电时间
    sj_gdsj = Column(Text, nullable=True, comment='项目归档时间')  # 项目归档时间
    htrl = Column(Float, nullable=True, comment='合同容量')  # 合同容量

    def to_dict(self):
        return {
            "id": self.id,
            "bao_name": self.bao_name,
            "bao_state": self.bao_state,
            "bao_lx": self.bao_lx,
            "bao_bm": self.bao_bm,
            "bao_city": self.bao_city,
            "bao_start": self.bao_start,
            "bao_end": self.bao_end,
            "is_reject": self.is_reject,
            "is_approve": self.is_approve,
            "is_submit": self.is_submit,
            "usercode": self.usercode,
            "gds": self.gds,
            "czry": self.czry,
            "gdlx": self.gdlx,
            "sj_ywsl": self.sj_ywsl,
            "sj_xckc": self.sj_xckc,
            "sj_fadf": self.sj_fadf,
            "sj_jgys": self.sj_jgys,
            "sj_zbsd": self.sj_zbsd,
            "sj_gdsj": self.sj_gdsj,
            "htrl": self.htrl,
        }


class Filelist(Base):
    __tablename__ = "filelist"

    id = Column(Integer, primary_key=True)
    file_name = Column(Text, nullable=False)
    file_state = Column(Integer, nullable=False)
    file_lx = Column(Integer, nullable=False)
    file_bz = Column(Text, nullable=False)
    bao_id = Column(Integer, nullable=False)
    file_start = Column(Text, nullable=False)
    file_end = Column(Text, nullable=False)
    file_hash = Column(Text, nullable=False)

    def to_dict(self):
        return {
            "id": self.id,
            "file_name": self.file_name,
            "file_state": self.file_state,
            "file_lx": self.file_lx,
            "file_bz": self.file_bz,
            "bao_id": self.bao_id,
            "file_start": self.file_start,
            "file_end": self.file_end,
            "file_hash": self.file_hash,
        }

# Pydantic模型定义
class LoginRequest(BaseModel):
    username: str
    password: str
    captcha: str

class LoginResponse(BaseModel):
    access_token: str
    username: str
    city: str
    need_password_change: bool

class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str
    confirm_password: str

class AddProjectRequest(BaseModel):
    bao_name: str
    bao_lx: str
    bao_bm: str
    bao_city: Optional[str] = None
    usercode: str
    gds: str
    htrl: Optional[float] = None
    gdlx: Optional[str] = None

class FileAuditRequest(BaseModel):
    file_lx: int
    bao_id: int
    state: int
    reason: Optional[str] = None

# JWT工具函数
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        phone: str = payload.get("sub")
        if phone is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        return phone
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# 依赖注入：获取当前用户
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    token = credentials.credentials
    phone = verify_token(token)
    user = db.query(User).filter(User.phone == phone).first()
    if user is None:
        raise HTTPException(status_code=401, detail="User not found")
    return user

# 日志记录装饰器
def log_api_call(operation_type: str):
    """API调用日志装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取request和current_user
            request = kwargs.get('request')
            current_user = kwargs.get('current_user')

            client_ip = get_client_ip(request) if request else "unknown"
            user_identity = None

            # 尝试获取用户身份
            if current_user:
                user_identity = current_user.phone

            try:
                result = await func(*args, **kwargs)
                return result

            except Exception as e:
                # 记录失败的操作（只记录未被函数内部处理的异常）
                logger.error(f"{operation_type}异常 - 用户: {mask_sensitive_data(user_identity, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
                raise

        return wrapper
    return decorator

# 判断文件后缀是否合规
def allowed_file(filename: str):
    return (
        "." in filename
        and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS
    )

# 生成验证码图片
def generate_captcha(width=120, height=40, chars_num=4):
    """生成验证码图片"""
    # 生成随机字符
    characters = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789"
    captcha_text = "".join(random.sample(characters, chars_num))

    # 创建图像
    image = Image.new("RGB", (width, height), color=(255, 255, 255))
    draw = ImageDraw.Draw(image)

    # 尝试使用系统字体
    try:
        font = ImageFont.truetype("arial.ttf", 28)
    except IOError:
        font = ImageFont.load_default()

    # 绘制字符
    for i, char in enumerate(captcha_text):
        draw.text(
            (15 + i * 25, random.randint(2, 10)),
            char,
            font=font,
            fill=(
                random.randint(0, 100),
                random.randint(0, 100),
                random.randint(0, 100),
            ),
        )

    # 添加干扰线
    for i in range(3):
        start_point = (random.randint(0, width // 3), random.randint(0, height))
        end_point = (random.randint(width // 3 * 2, width), random.randint(0, height))
        draw.line(
            [start_point, end_point],
            fill=(
                random.randint(100, 200),
                random.randint(100, 200),
                random.randint(100, 200),
            ),
            width=2,
        )

    # 添加干扰点
    for i in range(100):
        draw.point(
            (random.randint(0, width), random.randint(0, height)),
            fill=(
                random.randint(150, 250),
                random.randint(150, 250),
                random.randint(150, 250),
            ),
        )

    # 将图像转换为字节流
    image_bytes = io.BytesIO()
    image.save(image_bytes, format="JPEG")
    image_bytes.seek(0)

    return captcha_text, image_bytes

def generate_file_stream(file_path, chunk_size=20971520*2):
    """
    生成文件流，支持大文件的高效传输
    chunk_size: 每次读取的块大小，默认20MB（最大化单连接性能）
    """
    try:
        file_size = os.path.getsize(file_path)

        # 对于小于100MB的文件，直接读取到内存中一次性发送
        if file_size <= 104857600:  # 100MB
            with open(file_path, 'rb') as f:
                yield f.read()
        else:
            # 大文件使用分块读取
            with open(file_path, 'rb') as f:
                while True:
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    yield chunk
    except IOError as e:
        logger.error(f"文件读取错误: {str(e)}")
        raise

# API路由定义

# 验证码接口
@app.get("/captcha")
async def get_captcha(request: Request):
    """获取验证码接口"""
    client_ip = get_client_ip(request)
    logger.info(f"获取验证码 - IP: {client_ip}")

    captcha_text, image_bytes = generate_captcha()

    # 生成唯一ID
    captcha_id = str(uuid.uuid4())
    # 存储验证码到字典
    captcha_store[captcha_id] = captcha_text

    # 30秒后自动清除验证码
    import asyncio
    async def clear_captcha():
        await asyncio.sleep(30.0)
        if captcha_id in captcha_store:
            del captcha_store[captcha_id]

    # 创建后台任务清除验证码
    asyncio.create_task(clear_captcha())

    # 返回图片
    return Response(
        content=image_bytes.getvalue(),
        media_type="image/jpeg",
        headers={"X-Captcha-ID": captcha_id}
    )

# 用户登录接口
@app.post("/login", response_model=LoginResponse)
async def login(request: Request, login_data: LoginRequest):
    """用户登录接口"""
    client_ip = get_client_ip(request)
    captcha_id = request.headers.get("X-Captcha-ID")

    # 验证验证码
    if not captcha_id or captcha_id not in captcha_store:
        logger.warning(f"登录失败-验证码过期 - 用户: {mask_sensitive_data(login_data.username, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=401, detail="验证码已过期，请刷新")

    stored_captcha = captcha_store.get(captcha_id)

    if not stored_captcha or stored_captcha.lower() != login_data.captcha.lower():
        logger.warning(f"登录失败-验证码错误 - 用户: {mask_sensitive_data(login_data.username, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=401, detail="验证码错误")

    # 验证成功后删除验证码
    del captcha_store[captcha_id]

    # 获取数据库会话
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.phone == login_data.username).first()

        if not user or user.password != login_data.password:
            logger.warning(f"登录失败-用户名或密码错误 - 用户: {mask_sensitive_data(login_data.username, 'phone')} - IP: {client_ip}")
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        access_token = create_access_token(data={"sub": login_data.username})

        # 检查是否需要强制修改密码
        need_password_change = user.password == "123456"

        # 记录成功登录
        logger.info(f"登录成功 - 用户: {mask_sensitive_data(user.username, 'username')} - "
                   f"城市: {user.city} - IP: {client_ip}")

        return LoginResponse(
            access_token=access_token,
            username=user.username,
            city=user.city,
            need_password_change=need_password_change
        )
    finally:
        db.close()

# 密码修改接口（需要鉴权）
@app.post("/change_password")
async def change_password(
    request: Request,
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """修改密码接口"""
    client_ip = get_client_ip(request)

    # 参数验证
    if not password_data.current_password or not password_data.new_password or not password_data.confirm_password:
        logger.warning(f"修改密码失败-参数不完整 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="所有字段都是必填的")

    # 验证新密码确认
    if password_data.new_password != password_data.confirm_password:
        logger.warning(f"修改密码失败-密码不一致 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="新密码和确认密码不一致")

    # 验证新密码不能是默认密码
    if password_data.new_password == "123456":
        logger.warning(f"修改密码失败-使用默认密码 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="新密码不能是默认密码123456")

    # 验证新密码强度
    if len(password_data.new_password) < 6:
        logger.warning(f"修改密码失败-密码过短 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="密码长度至少6位")

    # 检查密码是否包含字母和数字
    has_letter = any(c.isalpha() for c in password_data.new_password)
    has_digit = any(c.isdigit() for c in password_data.new_password)
    if not (has_letter and has_digit):
        logger.warning(f"修改密码失败-密码复杂度不够 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="密码必须包含字母和数字")

    # 验证当前密码
    if current_user.password != password_data.current_password:
        logger.warning(f"修改密码失败-当前密码错误 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="当前密码错误")

    # 验证新密码不能与当前密码相同
    if password_data.new_password == password_data.current_password:
        logger.warning(f"修改密码失败-新旧密码相同 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="新密码不能与当前密码相同")

    # 更新密码
    current_user.password = password_data.new_password

    try:
        db.commit()
        # 记录密码修改成功日志
        logger.info(f"修改密码成功 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
        return {"msg": "密码修改成功"}
    except Exception as e:
        db.rollback()
        logger.error(f"修改密码失败-数据库错误 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip} - 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"密码修改失败: {str(e)}")

# 文件上传接口（需要鉴权）
@app.post("/upload")
async def upload_file(
    request: Request,
    bao_id: int = Form(...),
    file_type: int = Form(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """文件上传接口"""
    client_ip = get_client_ip(request)

    if not str(bao_id).isdigit() or bao_id <= 0:
        logger.warning(f"文件上传失败-项目ID无效 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="bao_id is not valid")

    if not str(file_type).isdigit() or file_type <= 0:
        logger.warning(f"文件上传失败-文件类型无效 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="file_type is not valid")

    baolist = db.query(Baolist).filter(Baolist.id == bao_id).first()
    if not baolist:
        logger.warning(f"文件上传失败-项目不存在 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="项目不存在")

    mb_max = PRO_TYPE_NUM_MAP.get(baolist.bao_lx)
    if mb_max is None:
        logger.error(f"文件上传失败-未知项目类型 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip} - 项目类型: {baolist.bao_lx}")
        raise HTTPException(status_code=400, detail=f"未知的项目类型: {baolist.bao_lx}")

    if file_type < 1 or file_type > mb_max:
        logger.warning(f"文件上传失败-文件类型超出范围 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail=f"file_type is error max:{mb_max}")

    city = current_user.city

    if city != "VIP":
        if baolist.bao_city != city:
            logger.warning(f"文件上传失败-权限不足 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
            raise HTTPException(status_code=400, detail="User exceeding authority")

    if not file.filename:
        logger.warning(f"文件上传失败-文件名为空 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="No selected file")

    l_file = db.query(Filelist).filter(
        Filelist.bao_id == bao_id,
        Filelist.file_lx == file_type,
    ).first()

    # 判断files如果查到传过就判断是不是审核中或者已通过
    if l_file:
        if l_file.file_state == 0:
            logger.warning(f"文件上传失败-文件审核中 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
            raise HTTPException(status_code=400, detail="文件已在审核中,无法再次上传")
        if l_file.file_state == 1:
            logger.warning(f"文件上传失败-文件已通过 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
            raise HTTPException(status_code=400, detail="文件已通过,无法再次上传")

    if file and allowed_file(file.filename):
        pattern = re.compile(r"[^\u4e00-\u9fa5a-zA-Z0-9]")
        lin_file_name = pattern.sub("", file.filename if file.filename is not None else "")

        # 安全处理文件名，避免索引越界
        if len(lin_file_name) > 4:  # 确保文件名长度足够
            base_name = lin_file_name[0:-3]  # 移除.pdf扩展名
        else:
            raise HTTPException(status_code=400, detail="No file name")

        filename = (
            time.strftime("%Y%m%d%H%M%S", time.localtime(time.time()))
            + "_"
            + base_name
            + ".pdf"
        )

        # 使用相对路径构建上传目录
        upload_subdir = os.path.join(
            "uploads",
            baolist.bao_city,
            baolist.bao_lx,
            time.strftime("%Y%m%d%H%M%S", time.localtime(int(baolist.bao_start)))
            + "_"
            + baolist.bao_name
            + "_"
            + baolist.bao_bm
        )
        path = os.path.join(BASE_DIR, upload_subdir)
        os.makedirs(path, exist_ok=True)

        # 保存文件
        file_path = os.path.join(path, filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        if l_file:
            old_file_path = os.path.join(path, l_file.file_name)
            if os.path.exists(old_file_path):
                os.remove(old_file_path)
            l_file.file_name = filename
            l_file.file_state = 0  # 0表示待审核
            l_file.file_lx = file_type
            l_file.bao_id = bao_id
            l_file.file_start = str(int(time.time()))
            l_file.file_hash = ""
            l_file.file_bz = ""
        else:
            file_str = Filelist(
                file_name=filename,
                file_state=0,  # 0表示待审核
                file_lx=file_type,
                bao_id=bao_id,
                file_start=str(int(time.time())),
                file_hash="",
                file_bz="",
                file_end=""
            )
            db.add(file_str)

        try:
            db.commit()
            # 记录文件上传成功
            logger.info(f"文件上传成功 - 用户: {mask_sensitive_data(current_user.username, 'username')} - "
                       f"项目: {baolist.bao_name} - 文件: {filename} - IP: {client_ip}")
        except Exception as e:
            db.rollback()
            logger.error(f"文件上传失败-数据库错误 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")

        # 查询是否全部提交 如果全部提交就修改状态 把is_submit改成false
        bao_mb_num = PRO_TYPE_NUM_MAP.get(baolist.bao_lx)
        if bao_mb_num is None:
            raise HTTPException(status_code=400, detail=f"未知的项目类型: {baolist.bao_lx}")

        filelist = db.query(Filelist).filter(Filelist.bao_id == bao_id).all()
        my_is_reject = False
        my_is_approve = False

        if len(filelist) >= bao_mb_num:
            baolist.is_submit = False
            try:
                db.commit()
            except Exception as e:
                db.rollback()
                raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")

        for item in filelist:
            if item.file_state == 2:  # 判断有没有文件是退回状态的 如果有就不清除包的退回状态
                my_is_reject = True
            if item.file_state == 0:  # 判断有没有文件是待审核状态的
                my_is_approve = True

        # 如果没有退回就清除包的退回状态
        if my_is_reject == False:
            baolist.is_reject = False
        else:
            baolist.is_reject = True
            baolist.bao_state = False

        if my_is_approve == False:
            baolist.is_approve = False
        else:
            baolist.is_approve = True
            baolist.bao_state = False

        baolist.czry = current_user.username
        try:
            db.commit()
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")

        return {"msg": "File uploaded successfully", "filename": filename}

    raise HTTPException(status_code=400, detail="File type not allowed")

# 文件下载接口（需要鉴权）
@app.get("/download")
async def download_file(
    request: Request,
    bao_id: int,
    file_lx: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """文件下载接口"""
    client_ip = get_client_ip(request)

    if bao_id <= 0 or file_lx <= 0:
        logger.warning(f"文件下载失败-参数无效 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="bao_id or file_lx is not valid")

    city = current_user.city
    baolist = db.query(Baolist).filter(Baolist.id == bao_id).first()
    if not baolist:
        logger.warning(f"文件下载失败-项目不存在 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="项目不存在")

    if city != "VIP":
        if baolist.bao_city != city:
            logger.warning(f"文件下载失败-权限不足 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
            raise HTTPException(status_code=400, detail="User exceeding authority")

    filelist = db.query(Filelist).filter(
        Filelist.bao_id == bao_id,
        Filelist.file_lx == file_lx
    ).first()

    if not filelist:
        logger.warning(f"文件下载失败-文件不存在 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="file not found")

    # 使用相对路径构建下载目录
    upload_subdir = os.path.join(
        "uploads",
        baolist.bao_city,
        baolist.bao_lx,
        time.strftime("%Y%m%d%H%M%S", time.localtime(int(baolist.bao_start)))
        + "_"
        + baolist.bao_name
        + "_"
        + baolist.bao_bm
    )
    directory = os.path.join(sys_temp, upload_subdir)

    file_path = os.path.join(directory, filelist.file_name)

    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.warning(f"文件下载失败-物理文件不存在 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=404, detail="文件不存在")

    # 获取文件大小
    file_size = os.path.getsize(file_path)

    # 检查是否支持断点续传
    range_header = request.headers.get('Range')
    if range_header:
        # 解析Range头
        byte_start = 0
        byte_end = file_size - 1

        if range_header.startswith('bytes='):
            range_match = range_header[6:].split('-')
            if range_match[0]:
                byte_start = int(range_match[0])
            if range_match[1]:
                byte_end = int(range_match[1])

        # 验证范围
        if byte_start >= file_size or byte_end >= file_size or byte_start > byte_end:
            raise HTTPException(status_code=416, detail="Range Not Satisfiable")

        # 创建部分内容响应
        def generate_partial_content():
            with open(file_path, 'rb') as f:
                f.seek(byte_start)
                remaining = byte_end - byte_start + 1
                while remaining > 0:
                    chunk_size = min(20971520*2, remaining)  # 20MB chunks
                    chunk = f.read(chunk_size)
                    if not chunk:
                        break
                    remaining -= len(chunk)
                    yield chunk

        headers = {
            'Content-Range': f'bytes {byte_start}-{byte_end}/{file_size}',
            'Content-Length': str(byte_end - byte_start + 1),
            'Accept-Ranges': 'bytes',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Content-Type': 'application/pdf'
        }

        return StreamingResponse(
            generate_partial_content(),
            status_code=206,  # Partial Content
            headers=headers,
            media_type="application/pdf"
        )
    else:
        # 完整文件传输
        headers = {
            'Content-Length': str(file_size),
            'Accept-Ranges': 'bytes',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Content-Type': 'application/pdf'
        }

        # 记录文件下载成功
        logger.info(f"文件下载成功 - 用户: {mask_sensitive_data(current_user.username, 'username')} - "
                   f"项目: {baolist.bao_name} - 文件: {filelist.file_name} - 大小: {file_size}字节 - IP: {client_ip}")

        return StreamingResponse(
            generate_file_stream(file_path, chunk_size=20971520*2),
            headers=headers,
            media_type="application/pdf"
        )

# 获取项目列表接口（需要鉴权）
@app.get("/baos")
async def list_baos(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取项目列表接口"""
    client_ip = get_client_ip(request)

    city = current_user.city
    if not current_user or city == "":
        logger.error(f"查询项目失败-用户或城市无效 - 手机: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="Bad username or city")

    # 根据用户权限查询项目列表
    if city == "VIP":
        baolist = db.query(Baolist).all()
        my_city = Sql_City
        logger.info(f"查询项目列表-VIP用户 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
    else:
        baolist = db.query(Baolist).filter(Baolist.bao_city == city).all()
        my_city = [city]
        logger.info(f"查询项目列表-普通用户 - 用户: {mask_sensitive_data(current_user.username, 'username')} - 城市: {city} - IP: {client_ip}")

    # 如果没有项目，直接返回空结果
    if not baolist:
        ret_cont = {}
        for City_item in my_city:
            # 动态生成项目类型字典结构
            ret_cont[City_item] = {item['name']: [] for item in Pro_Lx}
        return {
            "total": 0,
            "cont": ret_cont,
        }

    # 获取所有项目的ID列表
    bao_ids = [bao.id for bao in baolist]

    # 一次性查询所有相关的文件列表，避免N+1查询
    all_files = db.query(Filelist).filter(Filelist.bao_id.in_(bao_ids)).all()

    # 将文件按bao_id分组，提高查找效率
    files_by_bao_id = {}
    for file in all_files:
        if file.bao_id not in files_by_bao_id:
            files_by_bao_id[file.bao_id] = []
        files_by_bao_id[file.bao_id].append(file.to_dict())

    # 转换项目数据并添加文件信息
    cont = []
    for item in baolist:
        item_dict = item.to_dict()
        # 获取该项目的文件列表，如果没有文件则为空列表
        item_dict["files"] = files_by_bao_id.get(item.id, [])
        cont.append(item_dict)

    # 按城市和类型组织数据结构
    ret_cont = {}
    for City_item in my_city:
        # 动态生成项目类型字典结构
        ret_cont[City_item] = {item['name']: [] for item in Pro_Lx}

        # 只遍历属于当前城市的项目
        for Cont_item in cont:
            if City_item == Cont_item["bao_city"]:
                bao_lx = Cont_item["bao_lx"]
                ret_cont[City_item][bao_lx].append(Cont_item)

    return {
        "total": len(baolist),
        "cont": ret_cont,
    }

# 新增项目接口（需要鉴权）
@app.post("/addbao")
async def add_bao(
    request: Request,
    project_data: AddProjectRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """新增项目接口"""
    client_ip = get_client_ip(request)

    city = current_user.city
    if city == "VIP":
        bao_city = project_data.bao_city
    else:
        bao_city = city

    if bao_city not in Sql_City:
        raise HTTPException(status_code=400, detail=f"{bao_city}地区选择错误")

    if project_data.bao_lx not in PRO_TYPE_NUM_MAP:
        raise HTTPException(status_code=400, detail=f"{project_data.bao_lx}项目类型错误")

    if not project_data.bao_bm.isdigit():
        raise HTTPException(status_code=400, detail=f"{project_data.bao_bm}项目工单编号错误")

    # 验证usercode字段
    if not project_data.usercode or not isinstance(project_data.usercode, str):
        raise HTTPException(status_code=400, detail="户号不能为空")

    # 验证gds字段
    if not project_data.gds or not isinstance(project_data.gds, str):
        raise HTTPException(status_code=400, detail="供电所不能为空")

    # 验证合同容量字段
    if project_data.htrl is not None:
        if project_data.htrl < 0:
            raise HTTPException(status_code=400, detail="合同容量不能为负数")

    # 验证工单类型字段
    valid_gdlx = ["增容", "临时用电", "新装"]
    if project_data.gdlx and project_data.gdlx not in valid_gdlx:
        raise HTTPException(status_code=400, detail=f"工单类型错误，必须是：{', '.join(valid_gdlx)}")

    if (
        not project_data.bao_name
        or not project_data.bao_lx
        or not project_data.bao_bm
        or not bao_city
        or not project_data.usercode
        or not project_data.gds
    ):
        raise HTTPException(status_code=400, detail="参数不完整")

    m_bao = db.query(Baolist).filter(Baolist.bao_bm == project_data.bao_bm).first()
    if m_bao:
        raise HTTPException(status_code=400, detail="工单编号已存在")

    bao_start = int(time.time())
    bao_str = Baolist(
        bao_name=project_data.bao_name,
        bao_state=False,
        bao_lx=project_data.bao_lx,
        bao_bm=project_data.bao_bm,
        bao_city=bao_city,
        bao_start=str(bao_start),
        bao_end="",
        is_reject=False,
        is_approve=False,
        is_submit=True,
        usercode=project_data.usercode,
        gds=project_data.gds,
        czry=current_user.username,
        # 新增字段
        htrl=project_data.htrl,
        gdlx=project_data.gdlx,
        # 时间字段初始化为空，后续在工单流程中填写
        sj_ywsl=None,
        sj_xckc=None,
        sj_fadf=None,
        sj_jgys=None,
        sj_zbsd=None,
        sj_gdsj=None,
    )

    db.add(bao_str)
    try:
        db.commit()
        db.refresh(bao_str)  # 获取新插入记录的ID
        # 记录项目创建成功
        logger.info(f"创建项目成功 - 用户: {mask_sensitive_data(current_user.username, 'username')} - "
                   f"项目: {project_data.bao_name} - 编号: {project_data.bao_bm} - IP: {client_ip}")
    except Exception as e:
        db.rollback()
        logger.error(f"创建项目失败-数据库错误 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")

    # 处理特殊情况：高压用户项目在特定日期前的文件免收资
    b_time = project_data.bao_bm[0:8]
    maxtimes = 20240601
    if b_time[0:1] == "4":
        b_time = b_time[2:]
        maxtimes = 240601

    if project_data.bao_lx == "高压用户":
        if int(b_time) < maxtimes:
            file_str = Filelist(
                file_name="2024年6月1日前的项目此项无需收资",
                file_state=1,
                file_lx=4,
                file_start=str(int(time.time())),
                bao_id=bao_str.id,
                file_bz="",
                file_end=str(int(time.time())),
                file_hash=""
            )
            db.add(file_str)
            try:
                db.commit()
            except Exception as e:
                db.rollback()
                raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")

    return {"msg": "新增成功", "bao_id": bao_str.id}

# 删除项目接口（需要鉴权）
@app.get("/delbao")
async def del_bao(
    request: Request,
    bao_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除项目接口"""
    client_ip = get_client_ip(request)

    if not bao_id:
        logger.warning(f"删除项目失败-参数缺失 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="参数不完整")

    bao_str = db.query(Baolist).filter(Baolist.id == bao_id).first()
    if not bao_str:
        logger.warning(f"删除项目失败-项目不存在 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="项目不存在")

    city = current_user.city
    if city != bao_str.bao_city and city != "VIP":
        logger.warning(f"删除项目失败-权限不足 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="User exceeding authority")

    try:
        # 批量删除文件记录（更高效）
        deleted_files_count = db.query(Filelist).filter(Filelist.bao_id == bao_id).delete()

        # 删除项目记录
        db.delete(bao_str)

        # 统一提交事务，保证原子性
        db.commit()

        logger.warning(f"删除项目成功 - 用户: {mask_sensitive_data(current_user.username, 'username')} - "
                      f"项目: {bao_str.bao_name} - 删除文件数: {deleted_files_count} - IP: {client_ip}")

    except Exception as e:
        db.rollback()
        logger.error(f"删除项目失败-数据库错误 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

    # 清理文件系统中的相关文件
    try:
        upload_subdir = os.path.join(
            "uploads",
            bao_str.bao_city,
            bao_str.bao_lx,
            time.strftime("%Y%m%d%H%M%S", time.localtime(int(bao_str.bao_start)))
            + "_"
            + bao_str.bao_name
            + "_"
            + bao_str.bao_bm
        )
        path = os.path.join(sys_temp, upload_subdir)
        if os.path.exists(path):
            shutil.rmtree(path)
            logger.info(f"删除项目文件目录成功")
        else:
            logger.info(f"项目文件目录不存在，跳过删除")
    except Exception as e:
        # 文件删除失败不影响数据库删除结果，只记录警告
        logger.warning(f"删除项目文件目录失败 - 错误: {str(e)}")

    return {"msg": "删除成功"}

# 文件审核接口（需要鉴权）
@app.post("/file_pending")
async def file_shenhe(
    request: Request,
    audit_data: FileAuditRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """文件审核接口"""
    client_ip = get_client_ip(request)
    city = current_user.city

    if not audit_data.file_lx or not audit_data.bao_id or audit_data.state is None:
        logger.warning(f"文件审核失败-参数不完整 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="参数不完整")

    if audit_data.state not in [0, 1, 2]:
        logger.warning(f"文件审核失败-参数无效 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="文件状态错误0-2")

    if audit_data.state == 2 and not audit_data.reason:
        logger.warning(f"文件审核失败-缺少退回原因 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="退回原因不能为空")

    my_bao = db.query(Baolist).filter(Baolist.id == audit_data.bao_id).first()
    if not my_bao:
        logger.warning(f"文件审核失败-项目不存在 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="没有此项目")

    if city != "VIP" and city != my_bao.bao_city:
        logger.warning(f"文件审核失败-权限不足 - 用户: {mask_sensitive_data(current_user.username, 'username')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="用户越权")

    # 使用 filter_by 确保只获取一条记录
    file_str = db.query(Filelist).filter(
        Filelist.file_lx == audit_data.file_lx,
        Filelist.bao_id == audit_data.bao_id,
    ).first()

    if not file_str:
        logger.warning(f"文件审核失败-文件不存在 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip}")
        raise HTTPException(status_code=400, detail="文件不存在")

    # 记录审核操作详情
    state_names = {0: "待审核", 1: "通过", 2: "退回"}
    state_name = state_names.get(audit_data.state, "未知")

    if audit_data.state == 0:
        file_str.file_state = audit_data.state
        file_str.file_end = ""
        file_str.file_bz = ""
        my_bao.is_approve = True
    elif audit_data.state == 1:
        file_str.file_state = audit_data.state
        file_str.file_end = str(int(time.time()))
        file_str.file_bz = ""
    elif audit_data.state == 2:
        file_str.file_state = audit_data.state
        file_str.file_bz = audit_data.reason
        file_str.file_end = ""
        my_bao.is_reject = True

    try:
        db.commit()
        logger.info(f"文件审核成功 - 用户: {mask_sensitive_data(current_user.username, 'username')} - "
                   f"文件: {file_str.file_name} - 状态: {state_name} - IP: {client_ip}")
    except Exception as e:
        db.rollback()
        logger.error(f"文件审核失败-数据库错误 - 用户: {mask_sensitive_data(current_user.phone, 'phone')} - IP: {client_ip} - 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")

    bao_lx = my_bao.bao_lx
    my_file = db.query(Filelist).filter(Filelist.bao_id == audit_data.bao_id).all()
    my_is_reject = False
    my_is_approve = False
    bao_mb_num = PRO_TYPE_NUM_MAP.get(bao_lx)
    if bao_mb_num is None:
        raise HTTPException(status_code=400, detail=f"未知的项目类型: {bao_lx}")

    if len(my_file) >= bao_mb_num:
        for file in my_file:  # 查找所有文件是否有退回的
            if file.file_state == 2:
                my_is_reject = True
                break

        for file in my_file:  # 查找所有文件是否有待审核的
            if file.file_state == 0:
                my_is_approve = True
                break

         # 如果不存在退回或者待审核的文件，对应状态去掉
        if my_is_approve == False:
            my_bao.is_approve = False
        else:
            my_bao.is_approve = True
        if my_is_reject == False:
            my_bao.is_reject = False
        else:
            my_bao.is_reject = True

        try:
            db.commit()
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")

        if (
            my_is_approve == True or my_is_reject == True
        ):  # 如果存在退回或者待审核的文件，表示不能结束流程
            return {"msg": "操作成功"}

        my_bao.bao_state = True
        my_bao.bao_end = str(int(time.time()))
        my_bao.is_submit = False
        my_bao.is_approve = False
        my_bao.is_reject = False
        try:
            db.commit()
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=500, detail=f"数据库操作失败: {str(e)}")
        return {"msg": "审核完毕"}

    return {"msg": "操作成功"}

# 文件上传统计接口（无需鉴权）
@app.get("/file_stats")
async def file_upload_stats(
    request: Request,
    days: int = 7,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    key: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """文件上传统计接口"""
    client_ip = get_client_ip(request)

    # 权限控制：根据key参数过滤数据
    allowed_cities = None
    if key:
        if key in CITY_ACCESS_KEYS:
            city_permission = CITY_ACCESS_KEYS[key]
            if city_permission == "ALL":
                # 管理员key，可查看所有地市
                allowed_cities = Sql_City
                logger.info(f"文件统计-管理员访问 - Key: {key} - IP: {client_ip}")
            else:
                # 特定地市key，只能查看对应地市数据
                allowed_cities = [city_permission]
                logger.info(f"文件统计-地市访问 - Key: {key} - 城市: {city_permission} - IP: {client_ip}")
        else:
            logger.warning(f"文件统计-无效key - Key: {key} - IP: {client_ip}")
            raise HTTPException(status_code=403, detail="无效的访问权限")
    else:
        # 无key参数，拒绝访问
        logger.warning(f"文件统计-无权限访问被拒绝 - IP: {client_ip}")
        raise HTTPException(status_code=403, detail="访问被拒绝，请提供有效的访问权限")

    try:
        # 计算时间范围
        if start_date and end_date:
            # 使用自定义日期范围
            start_timestamp = int(
                datetime.strptime(start_date, "%Y-%m-%d").timestamp()
            )
            end_timestamp = int(
                datetime.strptime(
                    end_date + " 23:59:59", "%Y-%m-%d %H:%M:%S"
                ).timestamp()
            )
        else:
            # 使用天数计算
            current_time = int(time.time())
            start_timestamp = current_time - (days * 24 * 60 * 60)
            end_timestamp = current_time

        # 查询指定时间范围内的文件及其对应的项目信息（使用JOIN避免N+1查询）
        query = (
            db.query(Filelist, Baolist)
            .join(Baolist, Filelist.bao_id == Baolist.id)
            .filter(
                Filelist.file_start >= str(start_timestamp),
                Filelist.file_start <= str(end_timestamp),
            )
        )

        # 根据权限过滤城市数据
        if allowed_cities and len(allowed_cities) < len(Sql_City):
            # 如果不是查看所有城市，则添加城市过滤条件
            query = query.filter(Baolist.bao_city.in_(allowed_cities))

        files_with_projects = query.all()

        # 按日期分组统计
        daily_stats = {}

        for file, project in files_with_projects:
            # 将时间戳转换为日期
            file_date = time.strftime("%Y-%m-%d", time.localtime(int(file.file_start)))

            if file_date not in daily_stats:
                daily_stats[file_date] = {
                    "date": file_date,
                    "total_files": 0,
                    "files": [],
                }

            file_info = {
                "file_id": file.id,
                "file_name": file.file_name,
                "file_type": file.file_lx,
                "upload_time": time.strftime(
                    "%H:%M:%S", time.localtime(int(file.file_start))
                ),
                "project": {
                    "id": project.id,
                    "name": project.bao_name,
                    "code": project.bao_bm,
                    "type": project.bao_lx,
                    "city": project.bao_city,
                    "czry": project.czry,
                },
            }

            daily_stats[file_date]["files"].append(file_info)
            daily_stats[file_date]["total_files"] += 1

        # 转换为列表并按日期排序
        result = list(daily_stats.values())
        result.sort(key=lambda x: x["date"], reverse=True)

        # 计算总计
        total_files = sum(day["total_files"] for day in result)

        # 构建权限信息
        permission_info = {
            "has_key": key is not None,
            "allowed_cities": allowed_cities if allowed_cities else Sql_City,
            "is_admin": key == "admin2024" if key else False
        }

        logger.info(f"文件统计查询成功 - 总文件数: {total_files} - 权限城市: {len(allowed_cities) if allowed_cities else len(Sql_City)} - IP: {client_ip}")

        return {
            "success": True,
            "data": {
                "summary": {
                    "total_files": total_files,
                    "total_days": len(result),
                    "start_date": time.strftime(
                        "%Y-%m-%d", time.localtime(start_timestamp)
                    ),
                    "end_date": time.strftime(
                        "%Y-%m-%d", time.localtime(end_timestamp)
                    ),
                },
                "daily_stats": result,
                "permission": permission_info,
            },
        }

    except Exception as e:
        logger.error(f"文件统计查询失败 - IP: {client_ip} - 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 静态文件服务
app.mount("/static", StaticFiles(directory="."), name="static")

# 应用启动
if __name__ == "__main__":
    logger.info("开始启动服务器")
    logger.info("服务器配置 - 端口: 5000")

    uvicorn.run(
        "fastapi_app:app",
        host="0.0.0.0",
        port=5000,
        reload=False,  # 生产环境关闭自动重载
        workers=1,     # 单进程模式，避免共享状态问题
        access_log=True,
        log_level="info"
    )

logger.info("数据库连接成功")
logger.info("FastAPI应用配置完成")
