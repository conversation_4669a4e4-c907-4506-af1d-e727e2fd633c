#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI应用启动脚本
替代原来的Flask + Waitress启动方式
"""

import uvicorn
import os
import sys
from pathlib import Path

def main():
    """启动FastAPI应用"""
    
    # 确保当前目录在Python路径中
    current_dir = Path(__file__).parent
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # 设置环境变量
    os.environ.setdefault("PYTHONPATH", str(current_dir))
    
    print("=" * 60)
    print("🚀 启动华宇项目管理系统 (FastAPI版本)")
    print("=" * 60)
    print(f"📁 工作目录: {current_dir}")
    print(f"🌐 访问地址: http://localhost:5000")
    print(f"📚 API文档: http://localhost:5000/docs")
    print(f"🔧 ReDoc文档: http://localhost:5000/redoc")
    print("=" * 60)
    
    # 启动配置
    config = {
        "app": "fastapi_app:app",
        "host": "0.0.0.0",
        "port": 5000,
        "reload": False,  # 生产环境关闭自动重载
        "workers": 1,     # 单进程模式，避免共享状态问题
        "access_log": True,
        "log_level": "info",
        "loop": "auto",
        "http": "auto",
        # 性能优化配置
        "backlog": 2048,
        "limit_concurrency": 1000,
        "limit_max_requests": 10000,
        "timeout_keep_alive": 5,
        "timeout_graceful_shutdown": 30,
    }
    
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
